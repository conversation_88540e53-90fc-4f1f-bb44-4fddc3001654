# 技术实现建议

## 1. 依赖注入优化实现

### 1.1 创建依赖注入容器

```python
# utils/dependencies.py
from typing import Generator
from fastapi import Depends
from sqlalchemy.orm import Session
from services.auth_service import AuthService
from services.message_service import MessageService
from services.conversation_service import ConversationService
from services.llm_service import LLMService
from crud.database import get_database_session

# 服务依赖
def get_auth_service() -> AuthService:
    return AuthService()

def get_message_service() -> MessageService:
    return MessageService()

def get_conversation_service() -> ConversationService:
    return ConversationService()

def get_llm_service() -> LLMService:
    return LLMService()

# 数据库会话依赖
def get_db() -> Generator[Session, None, None]:
    db = get_database_session()
    try:
        yield db
    finally:
        db.close()
```

### 1.2 优化API路由

```python
# api/auth.py
from fastapi import APIRouter, Depends
from services.auth_service import AuthService
from utils.dependencies import get_auth_service

router = APIRouter(prefix="/api", tags=["认证"])

@router.post("/login", summary="用户登录")
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """用户登录接口"""
    try:
        is_authenticated = auth_service.authenticate_user(
            login_data.username, login_data.password
        )
        # ... 其余逻辑
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )
```

## 2. 缓存策略优化

### 2.1 增强缓存服务

```python
# services/cache_service.py
from typing import Optional, Any, Dict, List
import asyncio
from datetime import datetime, timedelta

class EnhancedCacheService:
    def __init__(self):
        self._memory_cache = {}
        self._memory_cache_ttl = {}
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }

    async def get_with_stats(self, key: str) -> Optional[Any]:
        """获取缓存并统计"""
        value = self.get(key)
        if value is not None:
            self._cache_stats["hits"] += 1
        else:
            self._cache_stats["misses"] += 1
        return value

    async def set_with_stats(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存并统计"""
        result = self.set(key, value, ttl)
        if result:
            self._cache_stats["sets"] += 1
        return result

    def get_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        total_requests = self._cache_stats["hits"] + self._cache_stats["misses"]
        hit_rate = (self._cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "type": "memory",
            "connected": True,
            "cache_size": len(self._memory_cache),
            "memory_usage": f"{len(str(self._memory_cache))} bytes",
            "hit_rate": f"{hit_rate:.2f}%",
            "stats": self._cache_stats
        }

    async def warm_up_cache(self, warm_up_data: Dict[str, Any]):
        """缓存预热"""
        for key, value in warm_up_data.items():
            await self.set_with_stats(key, value, 3600)  # 1小时TTL
```

### 2.2 RAG结果缓存

```python
# services/rag_cache_service.py
from typing import List, Dict, Optional
from services.cache_service import cache_service
import hashlib
import json

class RAGCacheService:
    def __init__(self):
        self.cache_service = cache_service

    def _generate_rag_key(self, query: str, collection_name: str, filters: Dict = None) -> str:
        """生成RAG缓存键"""
        key_data = f"rag:{collection_name}:{query}"
        if filters:
            key_data += f":{json.dumps(filters, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()

    async def cache_rag_result(
        self, 
        query: str, 
        collection_name: str, 
        documents: List[Dict], 
        ttl: int = 3600
    ) -> bool:
        """缓存RAG检索结果"""
        cache_key = self._generate_rag_key(query, collection_name)
        return await self.cache_service.set_with_stats(cache_key, documents, ttl)

    async def get_cached_rag_result(
        self, 
        query: str, 
        collection_name: str
    ) -> Optional[List[Dict]]:
        """获取缓存的RAG检索结果"""
        cache_key = self._generate_rag_key(query, collection_name)
        return await self.cache_service.get_with_stats(cache_key)

    async def invalidate_collection_cache(self, collection_name: str) -> int:
        """清除指定集合的缓存"""
        pattern = f"rag:{collection_name}:*"
        return self.cache_service.clear_pattern(pattern)
```

## 3. 数据库查询优化

### 3.1 优化数据访问层

```python
# crud/conversation_dao.py
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import and_, desc
from typing import List, Tuple, Optional
from models.conversation import Conversation
from models.message import Message

class OptimizedConversationDao:
    @staticmethod
    def get_by_username_with_messages(
        db: Session, 
        username: str, 
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[List[Conversation], int]:
        """获取用户会话列表（包含消息）"""
        # 使用joinedload避免N+1查询
        query = db.query(Conversation)\
            .options(
                joinedload(Conversation.messages)
                .joinedload(Message.references)
            )\
            .filter(Conversation.username == username)\
            .order_by(
                desc(Conversation.sticky_flag),
                desc(Conversation.updated_at)
            )

        # 获取总数
        total = query.count()
        
        # 分页
        conversations = query.offset(offset).limit(limit).all()
        
        return conversations, total

    @staticmethod
    def get_conversation_with_messages(
        db: Session, 
        conversation_id: int, 
        username: str
    ) -> Optional[Conversation]:
        """获取会话详情（包含消息）"""
        return db.query(Conversation)\
            .options(
                joinedload(Conversation.messages)
                .joinedload(Message.references)
            )\
            .filter(
                and_(
                    Conversation.id == conversation_id,
                    Conversation.username == username
                )
            ).first()
```

### 3.2 异步数据库支持

```python
# crud/async_database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator

# 创建异步引擎
async_engine = create_async_engine(
    settings.ASYNC_DATABASE_URL,
    echo=settings.DEBUG,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)

AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# 异步数据访问层
class AsyncConversationDao:
    @staticmethod
    async def get_by_username_async(
        session: AsyncSession, 
        username: str
    ) -> List[Conversation]:
        """异步获取用户会话列表"""
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload
        
        stmt = select(Conversation)\
            .options(selectinload(Conversation.messages))\
            .where(Conversation.username == username)\
            .order_by(Conversation.updated_at.desc())
        
        result = await session.execute(stmt)
        return result.scalars().all()
```

## 4. 安全性优化

### 4.1 增强JWT认证

```python
# services/enhanced_auth_service.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import jwt, JWTError
from fastapi import HTTPException, status
import secrets

class EnhancedAuthService:
    def __init__(self):
        self.blacklisted_tokens = set()
        self.refresh_tokens = {}

    def create_access_token(
        self,
        username: str,
        expires_delta: Optional[timedelta] = None,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建访问令牌"""
        if expires_delta is None:
            expires_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        expire = datetime.utcnow() + expires_delta
        
        to_encode = {
            "sub": username,
            "exp": expire,
            "iat": datetime.utcnow(),
            "jti": secrets.token_urlsafe(32),  # 唯一标识符
            "type": "access"
        }
        
        if additional_claims:
            to_encode.update(additional_claims)
        
        return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    def create_refresh_token(self, username: str) -> str:
        """创建刷新令牌"""
        expire = datetime.utcnow() + timedelta(days=7)
        
        to_encode = {
            "sub": username,
            "exp": expire,
            "iat": datetime.utcnow(),
            "jti": secrets.token_urlsafe(32),
            "type": "refresh"
        }
        
        token = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        self.refresh_tokens[token] = username
        return token

    def verify_token(self, token: str) -> Optional[str]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            
            # 检查令牌是否在黑名单中
            if token in self.blacklisted_tokens:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌已被撤销"
                )
            
            username: str = payload.get("sub")
            if username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌"
                )
            
            return username
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌"
            )

    def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                options={"verify_exp": False}
            )
            
            exp = payload.get("exp")
            if exp:
                expire_time = datetime.fromtimestamp(exp)
                remaining_time = expire_time - datetime.utcnow()
                
                if remaining_time.total_seconds() > 0:
                    self.blacklisted_tokens.add(token)
                    return True
            
            return True
        except JWTError:
            return False

    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """使用刷新令牌获取新的访问令牌"""
        if refresh_token not in self.refresh_tokens:
            return None
        
        username = self.refresh_tokens[refresh_token]
        return self.create_access_token(username)
```

### 4.2 速率限制中间件

```python
# middleware/rate_limit.py
import time
from typing import Dict, Tuple
from fastapi import Request, HTTPException, status
from collections import defaultdict
import asyncio

class RateLimiter:
    def __init__(self):
        self.requests: Dict[str, list] = defaultdict(list)
        self.lock = asyncio.Lock()

    async def is_allowed(
        self, 
        key: str, 
        max_requests: int, 
        window_seconds: int
    ) -> bool:
        """检查是否允许请求"""
        async with self.lock:
            now = time.time()
            window_start = now - window_seconds
            
            # 清理过期的请求记录
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if req_time > window_start
            ]
            
            # 检查请求数量
            if len(self.requests[key]) >= max_requests:
                return False
            
            # 记录新请求
            self.requests[key].append(now)
            return True

# 全局速率限制器实例
rate_limiter = RateLimiter()

async def rate_limit_middleware(request: Request, call_next):
    """速率限制中间件"""
    # 获取客户端标识
    client_id = request.client.host
    user_id = getattr(request.state, 'username', None)
    
    # 使用用户ID或客户端IP作为限制键
    limit_key = f"{user_id}:{client_id}" if user_id else client_id
    
    # 检查速率限制
    is_allowed = await rate_limiter.is_allowed(
        key=limit_key,
        max_requests=100,  # 每分钟100个请求
        window_seconds=60
    )
    
    if not is_allowed:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="请求过于频繁，请稍后再试"
        )
    
    response = await call_next(request)
    return response
```

## 5. 错误处理优化

### 5.1 统一错误处理装饰器

```python
# utils/error_handlers.py
import functools
import logging
from typing import Callable, Any
from fastapi import HTTPException
from schemas.response import StandardErrorResponse

logger = logging.getLogger(__name__)

def handle_exceptions(func: Callable) -> Callable:
    """统一异常处理装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except ValueError as e:
            logger.warning(f"Validation error in {func.__name__}: {e}")
            return StandardErrorResponse(
                code=1001,
                message="参数错误",
                details=str(e)
            )
        except Exception as e:
            logger.error(
                f"Unexpected error in {func.__name__}: {e}",
                exc_info=True
            )
            return StandardErrorResponse(
                code=1005,
                message="内部服务器错误"
            )
    return wrapper

# 使用示例
@router.post("/chat/stream")
@handle_exceptions
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    # 业务逻辑
    pass
```

### 5.2 自定义异常类

```python
# utils/exceptions.py
from typing import Optional, Dict, Any

class BaseCustomException(Exception):
    def __init__(
        self,
        message: str,
        error_code: str = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details
        super().__init__(self.message)

class AuthenticationError(BaseCustomException):
    def __init__(self, message: str = "认证失败"):
        super().__init__(message, "AUTH_ERROR")

class AuthorizationError(BaseCustomException):
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, "AUTHZ_ERROR")

class ValidationError(BaseCustomException):
    def __init__(self, message: str = "数据验证失败"):
        super().__init__(message, "VALIDATION_ERROR")

class RateLimitExceededError(BaseCustomException):
    def __init__(self, message: str = "请求过于频繁"):
        super().__init__(message, "RATE_LIMIT_ERROR")

class DatabaseError(BaseCustomException):
    def __init__(self, message: str = "数据库操作失败"):
        super().__init__(message, "DATABASE_ERROR")
```

## 6. 监控和日志优化

### 6.1 结构化日志

```python
# utils/enhanced_logging.py
import structlog
import logging
from typing import Dict, Any
from datetime import datetime

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

def get_structured_logger(name: str) -> structlog.BoundLogger:
    """获取结构化日志器"""
    return structlog.get_logger(name)

# 性能监控装饰器
def log_performance(logger_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger = get_structured_logger(logger_name or func.__module__)
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger.info(
                    "Function executed successfully",
                    function=func.__name__,
                    duration=duration,
                    duration_ms=duration * 1000
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Function execution failed",
                    function=func.__name__,
                    duration=duration,
                    duration_ms=duration * 1000,
                    error=str(e),
                    exc_info=True
                )
                raise
        return wrapper
    return decorator
```

### 6.2 健康检查增强

```python
# utils/health_check.py
import asyncio
from typing import Dict, Any
from datetime import datetime

class HealthChecker:
    def __init__(self):
        self.checks = {}

    def register_check(self, name: str, check_func: callable):
        """注册健康检查"""
        self.checks[name] = check_func

    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        
        for name, check_func in self.checks.items():
            try:
                start_time = time.time()
                result = await check_func()
                duration = time.time() - start_time
                
                results[name] = {
                    "status": "healthy" if result else "unhealthy",
                    "duration": duration,
                    "timestamp": datetime.utcnow().isoformat()
                }
            except Exception as e:
                results[name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
        
        return results

# 全局健康检查器
health_checker = HealthChecker()

# 注册健康检查
@health_checker.register_check("database")
async def check_database():
    return check_database_health()

@health_checker.register_check("cache")
async def check_cache():
    return await check_cache_health()

@health_checker.register_check("llm")
async def check_llm():
    # 检查LLM服务
    return True

# 增强的健康检查端点
@app.get("/health")
async def health_check():
    """详细健康检查"""
    results = await health_checker.run_all_checks()
    
    # 计算整体状态
    all_healthy = all(
        result["status"] == "healthy" 
        for result in results.values()
    )
    
    health_status = {
        "status": "healthy" if all_healthy else "unhealthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": results,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT
    }
    
    status_code = 200 if all_healthy else 503
    return JSONResponse(
        content=health_status,
        status_code=status_code
    )
```

## 7. 前端优化建议

### 7.1 虚拟滚动实现

```javascript
// static/js/virtual-scroll.js
class VirtualScroller {
    constructor(container, itemHeight = 60) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.items = [];
        this.visibleItems = [];
        this.scrollTop = 0;
        this.viewportHeight = container.clientHeight;
        
        this.init();
    }
    
    init() {
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
        this.render();
    }
    
    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.render();
    }
    
    render() {
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(this.viewportHeight / this.itemHeight) + 1,
            this.items.length
        );
        
        this.visibleItems = this.items.slice(startIndex, endIndex);
        this.updateDOM();
    }
    
    updateDOM() {
        // 更新DOM元素
        this.container.innerHTML = '';
        this.visibleItems.forEach(item => {
            const element = this.createItemElement(item);
            this.container.appendChild(element);
        });
    }
    
    createItemElement(item) {
        // 创建列表项元素
        const element = document.createElement('div');
        element.className = 'message-item';
        element.style.height = `${this.itemHeight}px`;
        element.innerHTML = this.renderItem(item);
        return element;
    }
    
    renderItem(item) {
        // 渲染单个消息项
        return `
            <div class="message ${item.role}">
                <div class="message-content">${item.content}</div>
                <div class="message-time">${item.timestamp}</div>
            </div>
        `;
    }
    
    setItems(items) {
        this.items = items;
        this.container.style.height = `${items.length * this.itemHeight}px`;
        this.render();
    }
}
```

### 7.2 状态管理优化

```javascript
// static/js/store.js
class ChatStore {
    constructor() {
        this.state = {
            conversations: [],
            currentConversation: null,
            messages: [],
            user: null,
            collections: [],
            loading: false,
            error: null
        };
        
        this.listeners = [];
    }
    
    subscribe(listener) {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }
    
    notify() {
        this.listeners.forEach(listener => listener(this.state));
    }
    
    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.notify();
    }
    
    // Actions
    async loadConversations() {
        this.setState({ loading: true });
        try {
            const response = await apiService.getConversations();
            this.setState({ 
                conversations: response.conversations,
                loading: false 
            });
        } catch (error) {
            this.setState({ 
                error: error.message,
                loading: false 
            });
        }
    }
    
    async sendMessage(message) {
        this.setState({ loading: true });
        try {
            const response = await apiService.sendStreamMessage(
                this.state.currentConversation.id,
                message
            );
            // 处理流式响应
            this.handleStreamResponse(response);
        } catch (error) {
            this.setState({ 
                error: error.message,
                loading: false 
            });
        }
    }
    
    handleStreamResponse(response) {
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        reader.read().then(function processText({ done, value }) {
            if (done) {
                return;
            }
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            lines.forEach(line => {
                if (line.startsWith('data: ')) {
                    const data = JSON.parse(line.slice(6));
                    // 更新消息状态
                    this.updateMessage(data);
                }
            });
            
            return reader.read().then(processText);
        }.bind(this));
    }
}

// 全局状态管理
const store = new ChatStore();
```

## 8. 部署优化

### 8.1 多阶段Docker构建

```dockerfile
# 多阶段构建优化
FROM python:3.12-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml ./

# 安装Python依赖
RUN pip install --user -e .

# 生产镜像
FROM python:3.12-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制Python包
COPY --from=builder /root/.local /root/.local

# 设置环境变量
ENV PATH=/root/.local/bin:$PATH
ENV PYTHONPATH=/root/.local/lib/python3.12/site-packages

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app
USER app

# 复制应用代码
COPY --chown=app:app . /app
WORKDIR /app

# 创建必要的目录
RUN mkdir -p logs static

# 暴露端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]
```

### 8.2 Kubernetes部署配置

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-backend
  labels:
    app: chatbot-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chatbot-backend
  template:
    metadata:
      labels:
        app: chatbot-backend
    spec:
      containers:
      - name: chatbot-backend
        image: chatbot-backend:latest
        ports:
        - containerPort: 8001
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: chatbot-service
spec:
  selector:
    app: chatbot-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8001
  type: LoadBalancer
```

## 9. 测试策略

### 9.1 单元测试

```python
# tests/test_auth_service.py
import pytest
from unittest.mock import Mock, patch
from services.auth_service import AuthService
from utils.exceptions import AuthenticationError

class TestAuthService:
    @pytest.fixture
    def auth_service(self):
        return AuthService()
    
    @pytest.fixture
    def mock_ldap_service(self):
        with patch('services.auth_service.LDAPService') as mock:
            yield mock
    
    def test_authenticate_user_success(self, auth_service, mock_ldap_service):
        """测试用户认证成功"""
        # 设置模拟
        mock_ldap_service.return_value.authenticate_user.return_value = True
        
        # 执行测试
        result = auth_service.authenticate_user("testuser", "password")
        
        # 验证结果
        assert result is True
        mock_ldap_service.return_value.authenticate_user.assert_called_once_with(
            "testuser", "password"
        )
    
    def test_authenticate_user_failure(self, auth_service, mock_ldap_service):
        """测试用户认证失败"""
        # 设置模拟
        mock_ldap_service.return_value.authenticate_user.return_value = False
        
        # 执行测试并验证异常
        with pytest.raises(AuthenticationError):
            auth_service.authenticate_user("testuser", "wrongpassword")
```

### 9.2 集成测试

```python
# tests/test_chat_api.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch
from main import app

client = TestClient(app)

class TestChatAPI:
    def test_chat_stream_authenticated(self):
        """测试认证用户的聊天流"""
        # 模拟认证
        with patch('utils.dependencies.get_current_username') as mock_auth:
            mock_auth.return_value = "testuser"
            
            # 发送请求
            response = client.post(
                "/api/chat/stream",
                json={
                    "message": "Hello",
                    "conversation_id": 1,
                    "collection_name": "test"
                }
            )
            
            # 验证响应
            assert response.status_code == 200
            assert "text/event-stream" in response.headers["content-type"]
    
    def test_chat_stream_unauthenticated(self):
        """测试未认证用户的聊天流"""
        response = client.post(
            "/api/chat/stream",
            json={
                "message": "Hello",
                "conversation_id": 1
            }
        )
        
        assert response.status_code == 401
```

### 9.3 性能测试

```python
# tests/test_performance.py
import asyncio
import time
import aiohttp
import pytest
from typing import List

class TestPerformance:
    @pytest.mark.asyncio
    async def test_chat_stream_performance(self):
        """测试聊天流性能"""
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            
            # 并发发送多个请求
            tasks = []
            for i in range(10):
                task = self.send_chat_message(session, f"Message {i}")
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # 验证性能
            assert end_time - start_time < 5.0  # 5秒内完成
            assert all(response.status == 200 for response in responses)
    
    async def send_chat_message(self, session, message):
        """发送聊天消息"""
        async with session.post(
            "http://localhost:8001/api/chat/stream",
            json={
                "message": message,
                "conversation_id": 1,
                "collection_name": "test"
            }
        ) as response:
            return response
```

## 10. 监控和告警

### 10.1 Prometheus指标

```python
# utils/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义指标
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

ACTIVE_CONNECTIONS = Gauge(
    'active_connections',
    'Number of active connections'
)

CACHE_HIT_RATIO = Gauge(
    'cache_hit_ratio',
    'Cache hit ratio'
)

# 中间件
async def metrics_middleware(request: Request, call_next):
    """指标收集中间件"""
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    # 记录指标
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    return response
```

### 10.2 告警规则

```yaml
# monitoring/alerts.yaml
groups:
- name: chatbot_alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"
  
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, http_request_duration_seconds) > 0.5
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"
  
  - alert: LowCacheHitRatio
    expr: cache_hit_ratio < 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low cache hit ratio"
      description: "Cache hit ratio is {{ $value }}"
```

这些技术实现建议提供了具体的代码示例和实现方案，可以帮助项目团队逐步优化系统性能、安全性和可维护性。建议根据项目实际情况和优先级分阶段实施这些优化。 