# 聊天机器人后端系统

一个基于 FastAPI 的智能聊天机器人后端系统，集成RAG（检索增强生成）技术，支持多轮对话、用户认证、知识库检索等功能。

## 🚀 项目特性

- **智能对话**: 集成大语言模型，支持多轮对话和上下文理解
- **RAG检索**: 集成Milvus向量数据库，支持知识库检索增强生成
- **用户认证**: 支持LDAP认证和JWT令牌管理
- **流式响应**: 支持SSE流式聊天，提供实时对话体验
- **会话管理**: 完整的对话历史记录和会话管理
- **缓存优化**: 内存缓存提升响应性能
- **标准化API**: RESTful API设计，统一响应格式
- **多知识库**: 支持多个知识库集合，灵活的过滤条件
- **引用功能**: AI回复包含引用来源，提高可信度

## 📁 项目结构

```
backend/
├── api/                    # API路由层
│   ├── auth.py            # 认证相关API
│   ├── chat.py            # 聊天相关API（SSE流式）
│   ├── conversations.py   # 会话管理API
│   ├── messages.py        # 消息管理API
│   └── collections.py     # 知识库集合API
├── config/                # 配置文件
│   └── settings.py        # 应用配置（Pydantic Settings）
├── crud/                  # 数据访问层
│   ├── conversation_dao.py # 会话数据访问
│   ├── message_dao.py     # 消息数据访问
│   └── database.py        # 数据库连接管理
├── models/                # SQLAlchemy数据模型
│   ├── conversation.py    # 会话模型
│   └── message.py         # 消息模型
├── schemas/               # Pydantic模式定义
│   ├── auth.py           # 认证模式
│   ├── conversation.py   # 会话模式
│   ├── message.py        # 消息模式
│   ├── collection.py     # 知识库集合模式
│   └── response.py       # 统一响应模式
├── services/              # 业务逻辑层
│   ├── auth_service.py   # 认证服务
│   ├── conversation_service.py # 会话服务
│   ├── message_service.py # 消息服务
│   ├── llm_service.py    # 大语言模型服务
│   ├── cache_service.py  # 缓存服务
│   └── ldap_service.py   # LDAP认证服务
├── rag/                   # RAG检索模块
│   ├── rag_retriever.py  # RAG检索器（LangChain集成）
│   ├── milvus_retriever.py # Milvus检索器
│   └── vectorstore_search.py # 向量存储搜索
├── utils/                 # 工具类
│   ├── dependencies.py   # FastAPI依赖注入
│   ├── exceptions.py     # 自定义异常
│   ├── exception_handlers.py # 全局异常处理器
│   └── logging_config.py # 日志配置
├── static/               # 静态资源
│   ├── chat.html        # 聊天界面
│   ├── login.html       # 登录界面
│   ├── css/            # 样式文件
│   └── js/             # JavaScript文件
├── docs/                 # 项目文档
└── main.py              # 应用入口
```

## 🛠️ 技术栈

- **Web框架**: FastAPI 0.116.0+
- **数据库**: SQLite (可扩展为PostgreSQL/MySQL)
- **向量数据库**: Milvus 2.3.0+
- **缓存**: 内存缓存（无需外部依赖）
- **认证**: LDAP + JWT
- **AI框架**: LangChain + OpenAI API
- **前端**: HTML + CSS + JavaScript (SSE)
- **包管理**: uv (推荐) 或 pip
- **配置管理**: Pydantic Settings

## 📋 环境要求

- Python 3.9+
- Milvus 向量数据库
- LDAP服务器 (可选，用于企业认证)

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd chatbot/backend
```

### 2. 安装依赖

```bash
# 使用uv安装依赖（推荐）
uv sync

# 或使用pip
pip install -e .
```

### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并配置相关参数：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```env
# 应用配置
APP_NAME=聊天系统
DEBUG=true
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./database.db

# 缓存配置
CACHE_TTL=3600

# JWT配置
JWT_SECRET_KEY=your-secret-key-change-in-production-32chars-minimum
ACCESS_TOKEN_EXPIRE_MINUTES=60

# LDAP配置
LDAP_URI=ldap://your-ldap-server:389
LDAP_BASE_DN=dc=users,dc=example,dc=com

# AI模型配置
OPENAI_API_KEY=your-api-key
OPENAI_API_URL=http://your-llm-server/v1
MODEL_CHAT=qwen3-32b
MODEL_EMBEDDING=bge-m3

# Milvus配置
MILVUS_URL=http://localhost:19530
MILVUS_USER=your-username
MILVUS_PASSWORD=your-password
MILVUS_DB_NAME=your-database
```

### 4. 启动服务

```bash
# 开发模式
uvicorn main:app --reload --host 0.0.0.0 --port 8001

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8001
```

### 5. 访问应用

- **API文档**: http://localhost:8001/docs
- **聊天界面**: http://localhost:8001/static/chat.html
- **登录界面**: http://localhost:8001/static/login.html
- **健康检查**: http://localhost:8001/health

## 📚 核心API接口

### 认证相关
- `POST /api/auth/login` - 用户登录（LDAP认证）
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 会话管理
- `GET /api/conversations` - 获取用户会话列表
- `POST /api/conversations` - 创建新会话
- `GET /api/conversations/{id}` - 获取会话详情
- `PUT /api/conversations/{id}` - 更新会话信息
- `DELETE /api/conversations/{id}` - 删除会话

### 消息管理
- `GET /api/conversations/{id}/messages` - 获取会话消息列表
- `DELETE /api/messages/{id}` - 删除指定消息

### 聊天功能（核心）
- `POST /api/chat/stream` - 发送消息（SSE流式响应）
- `GET /api/chat/stream` - 发送消息（GET方式，支持EventSource）

### 知识库集合
- `GET /api/collections` - 获取可用知识库集合列表

## 🔧 核心功能说明

### RAG检索增强生成

系统集成了先进的RAG技术：

1. **混合检索**: 结合语义检索和关键词检索
2. **父子文档**: 支持parent-child文档结构
3. **重排序**: 可选的文档重排序提高相关性
4. **引用追踪**: AI回复包含引用来源信息

### 流式聊天体验

- 使用SSE（Server-Sent Events）实现实时流式响应
- 支持POST和GET两种请求方式
- 自动保存对话历史
- 实时显示AI生成内容

### 多知识库支持

```python
# 知识库配置示例
COLLECTIONS_CONFIG = {
    "rcs": {
        "display_name": "RCS知识库",
        "description": "RCS相关文档",
        "filter": [
            {
                "name": "class_name",
                "display_name": "分类",
                "options": ["公共", "估值", "交易", "市场"]
            }
        ]
    }
}
```

### 安全认证

- LDAP企业认证集成
- JWT令牌管理
- 用户权限验证
- 会话安全控制

## 🧪 开发和测试

### 运行测试

```bash
# 安装测试依赖
uv add --group dev pytest pytest-asyncio pytest-cov

# 运行测试
pytest

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 代码格式化

```bash
# 安装开发工具
uv add --group dev black isort flake8

# 格式化代码
black .
isort .

# 代码检查
flake8 .
```

## 📦 部署

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv && uv sync --frozen
EXPOSE 8001

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]
```

### 生产部署

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8001

# 或使用uv运行
uv run uvicorn main:app --host 0.0.0.0 --port 8001
```

## 📊 监控和日志

### 健康检查

系统提供详细的健康检查端点：

```json
{
  "status": "healthy",
  "timestamp": **********.789,
  "services": {
    "database": "healthy",
    "cache": "healthy"
  },
  "version": "1.0.0",
  "environment": "production"
}
```

### 日志配置

- 结构化日志记录
- 请求响应时间监控
- 错误堆栈追踪
- 用户操作审计

## 🔧 配置说明

### 数据库配置

```python
# 生产环境建议使用PostgreSQL
DATABASE_URL = "postgresql://user:password@localhost/chatbot"
DB_POOL_SIZE = 20
DB_MAX_OVERFLOW = 30
```

### 缓存配置

```python
# 内存缓存配置
CACHE_TTL = 3600  # 1小时
```

### AI模型配置

```python
# 支持多种LLM提供商
API_URL = "http://your-llm-server/v1"
MODEL_CHAT = "qwen3-32b"
MODEL_EMBEDDING = "bge-m3"
MODEL_RERANK = "bge-reranker-v2-m3"
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [LangChain](https://langchain.com/) - AI应用开发框架
- [Milvus](https://milvus.io/) - 向量数据库
- [Pydantic](https://pydantic.dev/) - 数据验证库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至: <EMAIL>
- 项目文档: [docs/](docs/)

---

**注意**: 这是一个企业级RAG聊天系统，在生产环境部署前请确保：
1. 更改所有默认密钥和配置
2. 配置适当的安全策略
3. 设置监控和日志系统
4. 进行充分的测试