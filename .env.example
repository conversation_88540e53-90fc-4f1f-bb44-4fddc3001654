# 环境配置模板
# 复制此文件为 .env 并填入实际配置值

# =============================================================================
# 应用配置
# =============================================================================
DEBUG=false
ENVIRONMENT=production
APP_NAME=聊天系统
APP_VERSION=1.0.0

# =============================================================================
# 数据库配置
# =============================================================================
DATABASE_URL=sqlite:///./database.db
# 对于PostgreSQL: postgresql://user:password@localhost:5432/dbname
# 对于MySQL: mysql+pymysql://user:password@localhost:3306/dbname

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 - 请使用强密钥（至少32个字符）
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-32chars-minimum

# 加密密钥 - 请使用强密钥（至少32个字符）
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this-in-production

# JWT令牌过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=60

# =============================================================================
# LDAP配置
# =============================================================================
LDAP_URI=ldap://your-ldap-server:389
LDAP_BASE_DN=dc=users,dc=company,dc=com
LDAP_TIMEOUT=3

# =============================================================================
# CORS配置
# =============================================================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com


# =============================================================================
# RAG和AI模型配置
# =============================================================================
# 嵌入模型
MODEL_EMBEDDING=bge-m3

# 聊天模型
MODEL_CHAT=qwen3-32b

# 重排序模型
MODEL_RERANK=bge-reranker-v2-m3

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_URL=https://api.openai.com/v1
# 或者使用自部署的API: http://your-llm-server:8000/v1

# 重排序API地址
RERANK_API_URL=http://your-rerank-server:9997/

# =============================================================================
# Milvus向量数据库配置
# =============================================================================
MILVUS_URL=http://localhost:19530
MILVUS_USER=your-milvus-username
MILVUS_PASSWORD=your-milvus-password

# =============================================================================
# 速率限制配置
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# 开发环境配置示例
# =============================================================================
# 如果是开发环境，可以使用以下简化配置：

# DEBUG=true
# ENVIRONMENT=development
# DATABASE_URL=sqlite:///./dev_database.db
# JWT_SECRET_KEY=dev-secret-key-not-for-production-use-only
# ENCRYPTION_KEY=dev-encryption-key-not-for-production
# LDAP_URI=ldap://dev-ldap-server:389
# LDAP_BASE_DN=dc=dev,dc=company,dc=com
# OPENAI_API_KEY=sk-your-dev-api-key
# OPENAI_API_URL=http://localhost:8000/v1
# MILVUS_URL=http://localhost:19530
# MILVUS_USER=dev_user
# MILVUS_PASSWORD=dev_password
# REDIS_HOST=localhost
# REDIS_PORT=6379
# ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# =============================================================================
# 生产环境安全提示
# =============================================================================
# 1. 确保所有密钥都是强密钥（至少32个字符）
# 2. 不要在代码中硬编码任何敏感信息
# 3. 定期轮换密钥
# 4. 使用HTTPS
# 5. 限制CORS源
# 6. 配置防火墙规则
# 7. 启用日志监控
# 8. 定期备份数据库
