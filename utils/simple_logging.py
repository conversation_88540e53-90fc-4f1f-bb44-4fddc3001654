"""简化的结构化日志配置"""

import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path


class SimpleStructuredFormatter(logging.Formatter):
    """简化的结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1])
            }
        
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'exc_info',
                'exc_text', 'stack_info'
            }:
                try:
                    json.dumps(value)
                    log_data[key] = value
                except (TypeError, ValueError):
                    log_data[key] = str(value)
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


def setup_simple_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_console: bool = True,
    enable_structured: bool = True
) -> None:
    """设置简化的日志配置"""
    
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.handlers.clear()
    
    if enable_structured:
        formatter = SimpleStructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """获取简化的日志器"""
    logger = logging.getLogger(name)
    
    def log_with_context(level: int, msg: str, **kwargs):
        """带上下文的日志记录"""
        logger.log(level, msg, extra=kwargs)
    
    logger.info_with_context = lambda msg, **kwargs: log_with_context(logging.INFO, msg, **kwargs)
    logger.warning_with_context = lambda msg, **kwargs: log_with_context(logging.WARNING, msg, **kwargs)
    logger.error_with_context = lambda msg, **kwargs: log_with_context(logging.ERROR, msg, **kwargs)
    logger.debug_with_context = lambda msg, **kwargs: log_with_context(logging.DEBUG, msg, **kwargs)
    
    return logger


def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "INFO"):
    """记录安全事件日志"""
    logger = get_logger("security")
    
    security_data = {
        "security_event": event_type,
        "severity": severity,
        "details": details,
        "timestamp": datetime.now().isoformat()
    }
    
    level = getattr(logging, severity.upper(), logging.INFO)
    logger.log(level, f"安全事件: {event_type}", extra=security_data)


def log_request(method: str, path: str, status_code: int, duration: float, **kwargs):
    """记录请求日志"""
    logger = get_logger("request")
    
    request_data = {
        "request_method": method,
        "request_path": path,
        "status_code": status_code,
        "duration_ms": round(duration * 1000, 2),
        **kwargs
    }
    
    logger.info(f"{method} {path} - {status_code}", extra=request_data)


def log_error(error: Exception, context: Dict[str, Any] = None, **kwargs):
    """记录错误日志"""
    logger = get_logger("error")
    
    error_data = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "context": context or {},
        **kwargs
    }
    
    logger.error(f"错误: {error}", extra=error_data, exc_info=True)


def log_business_event(event_type: str, details: Dict[str, Any], **kwargs):
    """记录业务事件日志"""
    logger = get_logger("business")
    
    business_data = {
        "business_event": event_type,
        "details": details,
        **kwargs
    }
    
    logger.info(f"业务事件: {event_type}", extra=business_data)


def init_simple_logging():
    """初始化简化的日志配置"""
    try:
        from config.settings import settings
        log_level = getattr(settings, 'LOG_LEVEL', 'INFO')
        log_file = getattr(settings, 'LOG_FILE', 'logs/app.log')
        enable_structured = getattr(settings, 'ENABLE_STRUCTURED_LOGGING', True)
        
        setup_simple_logging(
            log_level=log_level,
            log_file=log_file,
            enable_console=True,
            enable_structured=enable_structured
        )
    except Exception as e:
        # 如果配置加载失败，使用默认配置
        setup_simple_logging()
        logging.warning(f"使用默认日志配置: {e}")
