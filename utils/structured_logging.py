"""结构化日志配置"""

import json
import logging
import sys
import traceback
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path

from config.settings import settings


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        
        # 基础日志信息
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info'
                }:
                    try:
                        # 确保值可以JSON序列化
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_data["extra"] = extra_fields
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class RequestContextFilter(logging.Filter):
    """请求上下文过滤器"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """添加请求上下文信息到日志记录"""
        
        # 尝试从上下文变量获取请求信息
        try:
            from contextvars import copy_context
            context = copy_context()
            
            # 添加请求ID（如果存在）
            request_id = getattr(record, 'request_id', None)
            if request_id:
                record.request_id = request_id
            
            # 添加用户ID（如果存在）
            user_id = getattr(record, 'user_id', None)
            if user_id:
                record.user_id = user_id
            
            # 添加会话ID（如果存在）
            session_id = getattr(record, 'session_id', None)
            if session_id:
                record.session_id = session_id
                
        except Exception:
            # 如果获取上下文失败，不影响日志记录
            pass
        
        return True


class PerformanceFilter(logging.Filter):
    """性能监控过滤器"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """添加性能相关信息"""
        
        # 添加执行时间（如果存在）
        if hasattr(record, 'duration'):
            record.performance = {
                "duration_ms": record.duration * 1000,
                "slow_query": record.duration > 1.0  # 超过1秒认为是慢查询
            }
        
        return True


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_console: bool = True,
    enable_structured: bool = True
) -> None:
    """
    设置应用日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        enable_console: 是否启用控制台输出
        enable_structured: 是否使用结构化日志格式
    """
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 创建格式化器
    if enable_structured:
        formatter = StructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    # 添加过滤器
    request_filter = RequestContextFilter()
    performance_filter = PerformanceFilter()
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.addFilter(request_filter)
        console_handler.addFilter(performance_filter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.addFilter(request_filter)
        file_handler.addFilter(performance_filter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    logging.info("日志系统初始化完成")


def get_logger(name: str) -> logging.Logger:
    """
    获取带有额外功能的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    logger = logging.getLogger(name)
    
    # 添加便捷方法
    def log_with_context(level: int, msg: str, **kwargs):
        """带上下文的日志记录"""
        extra = {}
        
        # 添加请求上下文
        if 'request_id' in kwargs:
            extra['request_id'] = kwargs.pop('request_id')
        if 'user_id' in kwargs:
            extra['user_id'] = kwargs.pop('user_id')
        if 'session_id' in kwargs:
            extra['session_id'] = kwargs.pop('session_id')
        
        # 添加性能信息
        if 'duration' in kwargs:
            extra['duration'] = kwargs.pop('duration')
        
        # 添加业务信息
        if 'business_data' in kwargs:
            extra['business'] = kwargs.pop('business_data')
        
        # 添加其他额外信息
        extra.update(kwargs)
        
        logger.log(level, msg, extra=extra)
    
    # 绑定便捷方法
    logger.info_with_context = lambda msg, **kwargs: log_with_context(logging.INFO, msg, **kwargs)
    logger.warning_with_context = lambda msg, **kwargs: log_with_context(logging.WARNING, msg, **kwargs)
    logger.error_with_context = lambda msg, **kwargs: log_with_context(logging.ERROR, msg, **kwargs)
    logger.debug_with_context = lambda msg, **kwargs: log_with_context(logging.DEBUG, msg, **kwargs)
    
    return logger


def log_performance(func_name: str, duration: float, **kwargs):
    """
    记录性能日志
    
    Args:
        func_name: 函数名称
        duration: 执行时间（秒）
        **kwargs: 额外信息
    """
    logger = get_logger("performance")
    
    performance_data = {
        "function": func_name,
        "duration": duration,
        "duration_ms": duration * 1000,
        **kwargs
    }
    
    level = logging.WARNING if duration > 1.0 else logging.INFO
    logger.log(level, f"函数执行完成: {func_name}", extra=performance_data)


def log_business_event(event_type: str, event_data: Dict[str, Any], **kwargs):
    """
    记录业务事件日志
    
    Args:
        event_type: 事件类型
        event_data: 事件数据
        **kwargs: 额外上下文信息
    """
    logger = get_logger("business")
    
    business_log_data = {
        "event_type": event_type,
        "event_data": event_data,
        **kwargs
    }
    
    logger.info(f"业务事件: {event_type}", extra={"business": business_log_data})


def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "INFO"):
    """
    记录安全事件日志
    
    Args:
        event_type: 安全事件类型
        details: 事件详情
        severity: 严重程度
    """
    logger = get_logger("security")
    
    security_data = {
        "security_event": event_type,
        "severity": severity,
        "details": details,
        "timestamp": datetime.now().isoformat()
    }
    
    level = getattr(logging, severity.upper(), logging.INFO)
    logger.log(level, f"安全事件: {event_type}", extra={"security": security_data})


# 性能监控装饰器
def log_execution_time(logger_name: str = None):
    """
    记录函数执行时间的装饰器
    
    Args:
        logger_name: 日志器名称
    """
    def decorator(func):
        import time
        import asyncio
        from functools import wraps
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                log_performance(
                    func.__name__, 
                    duration,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                log_performance(
                    func.__name__, 
                    duration,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# 初始化日志配置
def init_logging():
    """初始化应用日志配置"""
    log_level = getattr(settings, 'LOG_LEVEL', 'INFO')
    log_file = getattr(settings, 'LOG_FILE', 'logs/app.log')
    enable_structured = getattr(settings, 'ENABLE_STRUCTURED_LOGGING', True)
    
    setup_logging(
        log_level=log_level,
        log_file=log_file,
        enable_console=True,
        enable_structured=enable_structured
    )