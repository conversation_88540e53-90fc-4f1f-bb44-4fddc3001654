services:
  # FastAPI应用服务
  chatbot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chatbot-rag
    ports:
      - "8001:8001"
    environment:
      # 应用配置
      - DEBUG=true
      - ENVIRONMENT=development
      - APP_NAME=聊天系统
      - APP_VERSION=1.0.0
      
      # 数据库配置
      - DATABASE_URL=sqlite:///./database.db
      - DB_POOL_SIZE=20
      - DB_MAX_OVERFLOW=30
      - DB_POOL_TIMEOUT=30
      - DB_POOL_RECYCLE=3600
      
      # 安全配置
      - JWT_SECRET_KEY=wangzhixin
      - ENCRYPTION_KEY=wangzhixin111
      - ACCESS_TOKEN_EXPIRE_MINUTES=60
      
      # LDAP配置
      - LDAP_URI=ldap://**************:389
      - LDAP_BASE_DN=dc=users,dc=appdata,dc=erayt,dc=com
      - LDAP_TIMEOUT=10
      
      # CORS配置
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8000,http://localhost:63342
      
      # RAG和AI模型配置
      - MODEL_EMBEDDING=bge-m3
      - MODEL_CHAT=qwen3-32b
      - MODEL_RERANK=bge-reranker-v2-m3
      
      # API配置
      - OPENAI_API_KEY=sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553
      - OPENAI_API_URL=http://**************:23000/v1
      - RERANK_API_URL=http://**************:9997/
      
      # Milvus配置
      - MILVUS_URL=http://milvus:19530
      - MILVUS_USER=dify
      - MILVUS_PASSWORD=dify2025
      - MILVUS_DB_NAME=erayt_wiki
      - MILVUS_COLLECTION_NAME=erayt_wiki
      - MILVUS_CHUNK_STRATEGY=parent_child
      
      # 速率限制配置
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=logs/app.log
      - ENABLE_STRUCTURED_LOGGING=true
      
      # 服务器配置
      - HOST=0.0.0.0
      - PORT=8001

    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  chatbot_data:
    driver: local