# 生产环境配置模板
# 复制此文件为 .env.prod 并填入实际配置值

# =============================================================================
# 应用配置
# =============================================================================
DEBUG=false
ENVIRONMENT=production
APP_NAME=聊天系统
APP_VERSION=1.0.0

# =============================================================================
# 数据库配置
# =============================================================================
POSTGRES_PASSWORD=your_secure_database_password_here

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 - 请使用强密钥（至少32个字符）
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-32chars-minimum

# 加密密钥 - 请使用强密钥（至少32个字符）
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this-in-production

# JWT令牌过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=60

# =============================================================================
# LDAP配置
# =============================================================================
LDAP_URI=ldap://your-ldap-server:389
LDAP_BASE_DN=dc=users,dc=company,dc=com
LDAP_TIMEOUT=3

# =============================================================================
# CORS配置
# =============================================================================
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# Redis缓存配置
# =============================================================================
REDIS_PASSWORD=your_secure_redis_password_here
CACHE_TTL=3600

# =============================================================================
# RAG和AI模型配置
# =============================================================================
# 嵌入模型
MODEL_EMBEDDING=bge-m3

# 聊天模型
MODEL_CHAT=qwen3-32b

# 重排序模型
MODEL_RERANK=bge-reranker-v2-m3

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_URL=https://api.openai.com/v1

# 重排序API地址
RERANK_API_URL=http://your-rerank-server:9997/

# =============================================================================
# Milvus向量数据库配置
# =============================================================================
MILVUS_USER=your_milvus_username
MILVUS_PASSWORD=your_milvus_password

# =============================================================================
# MinIO对象存储配置
# =============================================================================
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key

# =============================================================================
# 速率限制配置
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# 生产环境安全提示
# =============================================================================
# 1. 确保所有密钥都是强密钥（至少32个字符）
# 2. 不要在代码中硬编码任何敏感信息
# 3. 定期轮换密钥
# 4. 使用HTTPS
# 5. 限制CORS源
# 6. 配置防火墙规则
# 7. 启用日志监控
# 8. 定期备份数据库