# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.db-shm
*.db-wal

# 环境配置
.env
.env.local
.env.production

# 测试
.coverage
.pytest_cache/
htmlcov/

# 文档
docs/
*.md
README*

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# 其他
.cache/
.mypy_cache/
.tox/
node_modules/