const { createApp, ref } = Vue;

createApp({
    setup() {
        const isLoggingIn = ref(false);
        const loginForm = ref({
            username: '',
            password: ''
        });
        const loginError = ref('');
        
        // 处理登录
        const handleLogin = async () => {
            if (!loginForm.value.username || !loginForm.value.password) {
                loginError.value = '请输入用户名和密码';
                return;
            }

            isLoggingIn.value = true;
            loginError.value = '';

            try {
                const result = await apiService.login(
                    loginForm.value.username,
                    loginForm.value.password
                );

                if (result.success) {
                    localStorage.setItem('currentUser', result.user.username);
                    window.location.href = '/static/chat.html';
                } else {
                    loginError.value = result.message || '登录失败';
                }
            } catch (error) {
                loginError.value = '网络连接失败';
                console.error('Login error:', error);
            } finally {
                isLoggingIn.value = false;
            }
        };

        return {
            isLoggingIn,
            loginForm,
            loginError,
            handleLogin
        };
    }
}).mount('#app');